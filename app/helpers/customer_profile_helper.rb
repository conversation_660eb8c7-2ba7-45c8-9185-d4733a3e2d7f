module CustomerProfileHelper

  def customer_name_helper(format, name = nil)
   full_name = name || session_profile.customer_name
   split_name = full_name.split(' ')
   first_name = split_name[0]
   last_name = split_name[1]
   initials = first_name[0]
   initials += last_name[0] if last_name.present?
   initials.upcase!

   case format
    when :full
      full_name
    when :first
      first_name
    when :initials
      initials
   end
  end

  def random_icon_colour
    ['#BFF457', '#1F9E86', '#76E2F4', '#DC2454', '#FAB43F'].sample
  end

  def sorted_supplier_orders_for(suppliers, orders)
    # gather supplier along with their recent order
    supplier_orders = suppliers.map do |supplier|
      recent_order = orders.detect{|order| order.supplier_profiles.include?(supplier) }
      [
        supplier,
        recent_order
      ]
    end.compact.to_h

    # sort list by delivery at if presebt
    supplier_orders.sort_by do |_, recent_order|
      case
      when recent_order.present? && recent_order.delivery_at >= Time.zone.now # supplier with upcoming orders sorted first
        [recent_order.delivery_at.to_i, recent_order.delivery_at.to_i]
      when recent_order.present? # supplier with recent past orders sorted after upcoming orders
        [(Time.zone.now + 6.months).to_i, -1 * recent_order.delivery_at.to_i]
      else # suppliers with no recent order sorted last
         [(Time.zone.now + 1.year).to_i, (Time.zone.now + 1.year).to_i]
      end
    end
  end

  # Returns the total count of quote requests and quoted orders for a customer
  # This matches the logic used in the quotes API and frontend
  def quote_count_for(customer)
    return 0 if customer.nil?

    Rails.cache.fetch("customer_profile_#{customer.id}_quote_count", expires_in: 12.hours) do
      quote_requests_count = customer.quotes.where(status: %w[submitted quoted]).count
      quoted_orders_count = customer.orders.where(status: 'quoted').count
      quote_requests_count + quoted_orders_count
    end
  end

end
