class Orders::Confirm

  def initialize(order:, auto_confirmation: false)
    @order = order
    @auto_confirmation = auto_confirmation
    @is_reactivation = order.present? && order.status == 'paused'
    @result = Result.new(order: order)
  end

  def call
    if can_confirm?
      was_quoted = order.status == 'quoted'
      sanitize_order_lines
      if order.update(status: 'confirmed')
        result.order = order
        invalidate_quote_count_cache if was_quoted
        send_confirmation_notification
      else
        result.errors += order.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :order, :auto_confirmation, :result

  def can_confirm?
    case
    when order.blank?
      result.errors << 'Cannot confirm a missing order'
    when order.status == 'confirmed'
      result.errors << 'Cannot confirm a an already confirmed order'
    when %w[new amended paused].exclude?(order.status)
      result.errors << "Cannot confirm this order (current status = #{order.status})"
    when !auto_confirmation && pending_order_lines.present?
      result.errors << "#{pending_order_lines.count} order line(s) have not been accepted"
    end
    result.errors.blank?
  end

  def sanitize_order_lines
    remove_unconfirmed_order_lines if order.is_team_order?
    confirmed_order_lines.update_all(status: 'accepted') if auto_confirmation
  end

  def confirmed_order_lines
    return @_confirmed_order_lines if @_confirmed_order_lines.present?

    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
      paid_only: order.is_team_order? && order.attendee_pays,
    }
    @_confirmed_order_lines = OrderLines::List.new(options: lister_options).call
  end

  def remove_unconfirmed_order_lines
    remaining_order_lines = order.order_lines - confirmed_order_lines
    remaining_order_lines.each(&:destroy)
  end

  def send_confirmation_notification
    return if @is_reactivation && order.order_type == 'recurrent' && order.template_id != order.id
    return if order.is_team_order? && auto_confirmation

    Customers::Emails::SendOrderConfirmationEmail.new(customer: order.customer_profile, order: order).delay(queue: :notifications).call
  end

  def pending_order_lines
    return nil if confirmed_order_lines.blank?

    @_pending_order_lines ||= confirmed_order_lines.where.not(status: 'accepted')
  end

  def invalidate_quote_count_cache
    return if order.customer_profile.blank?
    Rails.cache.delete("customer_profile_#{order.customer_profile.id}_quote_count")
  end

  class Result
    attr_accessor :order, :errors

    def initialize(order:)
      @order = order
      @errors = []
    end

    def success?
      errors.blank? && order.present? && order.status == 'confirmed'
    end
  end

end
