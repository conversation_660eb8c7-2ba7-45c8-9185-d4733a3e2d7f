class Orders::Submit

  def initialize(order:, order_params: {}, customer: nil)
    @order = order
    @order_params = order_params
    @customer = order&.customer_profile || customer
    @result = Result.new
  end

  def call
    orders.each do |order|
      next if order.order_lines.blank?

      save_order(order)
    end
    if result.success?
      update_customer_last_ordered_at
      self.delay(queue: :notifications).log_submission_event
    end
    result
  end

private

  attr_accessor :order, :customer, :order_params, :result

  def orders
    if order.is_recurrent?
      Order.where(recurrent_id: order.recurrent_id)
    else
      [order]
    end
  end

  def save_order(order)
    was_quoted = order.status == 'quoted'
    order_updater = Orders::Update.new(order: order, order_params: sanitized_params(order), profile: customer).call
    if order_updater.success?
      checkout_woolworths_order(order) if order.has_woolworths_items?
      log_amazon_event if order.supplier_profiles.where(id: yordar_credentials(:amazon, :supplier_profile_id)).present?
      update_customer_quote if order.customer_quote_id.present?
      sync_promotions_for(order)
      send_customer_email_for(order)
      send_supplier_email_for(order)
      save_major_category_for(order)
      redeem_coupon_for(order) if order.coupon.present?
      invalidate_quote_count_cache_for(order) if was_quoted && order.status != 'quoted'
      if order.is_recurrent?
        result.order ||= order_updater.order
        result.recurrent_orders << order_updater.order
      else
        result.order = order_updater.order
        create_on_hold_charge_for(order)
        send_account_manager_email_for(order)
      end
    else
      result.errors += order_updater.errors
    end
  end

  def sanitized_params(order)
    [
      default_submission_params,
      purchase_order_params,
      delivery_params_for(order),
      order_params.except(:po_number, :cpo_id, :gst_free_cpo_id, :delivery_at)
    ].inject(&:merge)
  end

  def default_submission_params
    {
      status: 'new',
      customer_profile: (order.customer_profile || customer),
    }
  end

  def purchase_order_params
    params = {}
    params[:customer_purchase_order] = customer_purchase_order if customer_purchase_order.present?
    params[:gst_free_customer_purchase_order] = gst_free_customer_purchase_order if gst_free_customer_purchase_order.present?
    params
  end

  def customer_purchase_order
    @_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:cpo_id]).call
  end

  def gst_free_customer_purchase_order
    @_gst_free_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:gst_free_cpo_id]).call
  end

  def delivery_params_for(order)
    return {} if order_params[:delivery_at].blank?

    passed_delivery_at = order_params[:delivery_at].is_a?(String) ? Time.zone.parse(order_params[:delivery_at]) : order_params[:delivery_at]
    delivery_at = case
    when order.is_recurrent?
      Orders::GetNextRecurringDeliveryAt.new(order: order, new_delivery_at: passed_delivery_at).call
    else
      passed_delivery_at
    end
    {
      delivery_at: delivery_at
    }
  end

  def checkout_woolworths_order(order)
    Woolworths::API::CheckoutOrder.new(order: order).delay(queue: :instant, attempts: 5).call # pay for the woolworths order
  end

  def sync_promotions_for(order)
    Promotions::SyncWithOrder.new(order: order, customer: customer).call
  end

  def send_customer_email_for(order)
    Customers::Emails::SendNewOrderEmail.new(order: order, customer: customer).delay(queue: :notifications).call
  end

  def suppliers_to_notify_for(order)
    order.supplier_profiles.map do |supplier|
      order.order_suppliers.where(supplier_profile: supplier).first_or_create # create for notification document version
      supplier
    end
  end

  def send_supplier_email_for(order)
    suppliers_to_notify_for(order).each do |supplier|
      Suppliers::Emails::SendNewOrderEmail.new(order: order, supplier: supplier).delay(queue: :notifications).call
    end
  end

  def send_account_manager_email_for(order)
    Orders::Notifications::NotifyAccountManagers.new(order: order).delay(queue: :notifications).call
  end

  def save_major_category_for(order)
    Orders::RetrieveMajorOrderCategory.new(order: order, save_category: true).delay(queue: :data_integrity).call
  end

  def redeem_coupon_for(order)
    Coupons::Redeem.new(coupon: order.coupon, order: order).call
  end

  def update_customer_quote
    customer_quote = order.customer_quote
    return if customer_quote.blank? || order.customer_quote.status == 'accepted'

    order.customer_quote.update(status: 'accepted')
  end

  def create_on_hold_charge_for(order)
    order_card = order.credit_card
    return if order.is_recurrent? || order_card.blank? || order_card.pay_on_account? || order_card.stripe_token.blank?

    Orders::OnHoldCharges::Upsert.new(order: order).delay(queue: :data_integrity).call
  end

  def log_submission_event
    event_type = 'new-order-submitted'
    event_info = {}
    lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order).call
    if !lead_time_fetcher.can_process?
      event_info[:after_cutoff] = true
    end

    supplier_spends_fetcher = Orders::GetSupplierSpends.new(order: order, exclude_surcharge: true).call
    if supplier_spends_fetcher.is_under?
      event_info[:under_supplier_minimum] = true
      event_info[:supplier_spends] = supplier_spends_fetcher.supplier_spends.select(&:is_under?).map do |supplier_spend|
        {
          name: supplier_spend.supplier.name,
          minimum: supplier_spend.minimum_spend,
          remaining: supplier_spend.remaining_spend
        }
      end
    end

    if order.is_event_order?
      event_type = 'new-custom-order-submitted'
      check_order_margin
    end
    event_severity = event_info.present? ? 'warning' : 'info'
    EventLogs::Create.new(event_object: order, event: event_type, severity: event_severity, **event_info).call
  end

  def check_order_margin
    return if order.commission.blank?

    markdown = 0.0
    yordar_commission = ((1 - (1 - (markdown / 100)) / (1 + (order.commission.to_f / 100))) * 100).round(2)
    return if yordar_commission >= Order::CUSTOM_ORDER_COMMISSION_THRESHOLD

    EventLogs::Create.new(event_object: order, event: 'order-below-margin-threshold', severity: 'warning', commission: yordar_commission)
  end

  def log_amazon_event
    EventLogs::Create.new(event_object: order, event: 'new-amazon-order', severity: 'warning').call
  end

  def update_customer_last_ordered_at
    return if !Rails.env.production? && !Rails.env.test?

    Hubspot::SyncContact.new(contact: customer.user).delay(attempts: 1).call
  end

  def invalidate_quote_count_cache_for(order)
    return if order.customer_profile.blank?
    Rails.cache.delete("customer_profile_#{order.customer_profile.id}_quote_count")
  end

  class Result
    attr_accessor :order, :recurrent_orders, :errors

    def initialize
      @order = nil
      @recurrent_orders = []
      @errors = []
    end

    def success?
      errors.blank? && order.present? && order.status == 'new'
    end
  end
end

