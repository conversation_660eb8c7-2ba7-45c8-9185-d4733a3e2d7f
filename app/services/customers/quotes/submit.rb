class Customers::Quotes::Submit

  def initialize(form_data:, profile: nil)
    @form_data = form_data
    @profile = profile
    @result = Result.new
  end

  def call
    if can_quote?
      create_quote
      if result.quote.present?
        notify_admin
        notify_cutomer
        log_event
      end
    end
    result
  end

private

  attr_reader :form_data, :profile, :result

  def can_quote?
    case
    when profile.present? && !profile.is_a?(CustomerProfile)
      result.errors << 'Only customers can submit a quote'
    when quote_kind.blank?
      result.errors << 'Need quote type'
    end
    result.errors.blank?
  end

  def create_quote
    quote = CustomerQuote.new(sanitized_quote_params)
    if quote.save
      result.quote = quote
      invalidate_quote_count_cache
    else
      result.errors += quote.errors.full_messages
    end
  end

  def notify_admin
    Admin::Emails::SendCustomerQuoteEmail.new(quote: result.quote).delay(queue: :notifications).call
  end

  def notify_cutomer
    Customers::Emails::SendCustomerQuoteEmail.new(quote: result.quote).delay(queue: :notifications).call
  end

  def log_event
    EventLogs::Create.new(event_object: result.quote, event: 'new-quote-submitted').delay(queue: :notifications).call
  end

  def sanitized_quote_params
    quote_params = {
      kind: quote_kind,
      form_data: form_data,
      customer_profile: profile
    }
    [default_params, quote_params].inject(&:merge)
  end

  def default_params
    {
      status: 'submitted',
      uuid: SecureRandom.uuid
    }
  end

  def quote_kind
    @_quote_kind ||= form_data.present? && form_data[:type]
  end

  def invalidate_quote_count_cache
    return if profile.blank?
    Rails.cache.delete("customer_profile_#{profile.id}_quote_count")
  end

  class Result
    attr_accessor :quote, :errors

    def initialize
      @quote = nil
      @errors = []
    end

    def success?
      errors.blank? && quote.present?
    end
  end

end