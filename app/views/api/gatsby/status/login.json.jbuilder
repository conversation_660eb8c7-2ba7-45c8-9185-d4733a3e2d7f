if logged_in_user.present?
  json.user do
    json.id logged_in_user.id
    json.email logged_in_user.email
    json.first_name logged_in_user.firstname
    json.last_name logged_in_user.lastname
    user_type = type_of_user(user: logged_in_user, profile: logged_in_session_profile)
    json.type user_type
    json.signed_in_as_admin is_admin?
    if user_type == 'customer'
      customer = logged_in_session_profile
      json.profile do
        json.id customer.id
        json.company_id customer.company_id
        json.quote_count quote_count_for(customer)
      end
      if @needs_contact_details
        json.phone logged_in_user.customer_profile.contact_phone
        json.company logged_in_user.customer_profile.company_name
      end
    end
  end
else
  json.user nil
end


if (flashes = flash.presence)
  json.notifications flashes.map do |type, message|
    json.type type
    json.message message
  end
  flash.clear
end

