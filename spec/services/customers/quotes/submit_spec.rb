require 'rails_helper'

RSpec.describe Customers::Quotes::Submit, type: :service, customers: true, quotes: true do

  let!(:form_data) do
    {
      type: CustomerQuote::VALID_QUOTE_KINDS.sample,
      field1: 'value1',
      field2: 'value2',
    }
  end

  before do
    # mock admin email
    admin_email_sender = admin_delayed_email_sender = double(Admin::Emails::SendCustomerQuoteEmail)
    allow(Admin::Emails::SendCustomerQuoteEmail).to receive(:new).and_return(admin_email_sender)
    allow(admin_email_sender).to receive(:delay).and_return(admin_delayed_email_sender)
    allow(admin_delayed_email_sender).to receive(:call).and_return(true)

    # mock customer email
    customer_email_sender = customer_delayed_email_sender = double(Customers::Emails::SendCustomerQuoteEmail)
    allow(Customers::Emails::SendCustomerQuoteEmail).to receive(:new).and_return(customer_email_sender)
    allow(customer_email_sender).to receive(:delay).and_return(customer_delayed_email_sender)
    allow(customer_delayed_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'creates and saves a CustomerQuote record' do
    quote_submitter = Customers::Quotes::Submit.new(form_data: form_data).call

    expect(quote_submitter).to be_success

    created_quote = quote_submitter.quote
    expect(created_quote).to be_present
    expect(created_quote).to be_persisted
    expect(created_quote).to be_a(CustomerQuote)
  end

  it 'creates the CustomerQuote record with passed in data and defaults' do
    quote_submitter = Customers::Quotes::Submit.new(form_data: form_data).call

    expect(quote_submitter).to be_success
    created_quote = quote_submitter.quote
    expect(created_quote.kind).to eq(form_data[:type])
    expect(created_quote.status).to eq('submitted')
    expect(created_quote.uuid).to be_present
    expect(created_quote.form_data).to eq(JSON.parse(form_data.to_json))
  end

  it 'creates a quote associated to the passed in customer' do
    quote_profile = create(:customer_profile, :random)

    quote_submitter = Customers::Quotes::Submit.new(form_data: form_data, profile: quote_profile).call

    expect(quote_submitter).to be_success
    created_quote = quote_submitter.quote
    expect(created_quote.customer_profile).to eq(quote_profile)

    expect(quote_profile.reload.quotes).to include(created_quote)
  end

  it 'notifies the admin about the quote', notifications: true do
    expect(Admin::Emails::SendCustomerQuoteEmail).to receive(:new).with(quote: anything) # quote created within SO

    quote_submitter = Customers::Quotes::Submit.new(form_data: form_data).call
    expect(quote_submitter).to be_success
  end

  it 'notifies the customer about the quote' do
    expect(Customers::Emails::SendCustomerQuoteEmail).to receive(:new).with(quote: anything) # quote created within SO

    quote_submitter = Customers::Quotes::Submit.new(form_data: form_data).call
    expect(quote_submitter).to be_success
  end

  it 'logs a `New Quote Submitted` event', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'new-quote-submitted') # event_object is the created customer quote

    quote_submitter = Customers::Quotes::Submit.new(form_data: form_data).call
    expect(quote_submitter).to be_success
  end

  context 'cache invalidation' do
    let!(:customer) { create(:customer_profile, :random) }
    let!(:form_data_with_customer) { form_data.merge(customer_profile_id: customer.id) }

    it 'invalidates the quote count cache after successful quote creation' do
      cache_key = "customer_profile_#{customer.id}_quote_count"

      Rails.cache.write(cache_key, 5)
      expect(Rails.cache.exist?(cache_key)).to be_truthy

      quote_submitter = Customers::Quotes::Submit.new(form_data: form_data_with_customer).call
      expect(quote_submitter).to be_success
      expect(Rails.cache.exist?(cache_key)).to be_falsey
    end

    it 'does not invalidate cache if quote creation fails' do
      cache_key = "customer_profile_#{customer.id}_quote_count"

      Rails.cache.write(cache_key, 5)
      expect(Rails.cache.exist?(cache_key)).to be_truthy

      invalid_form_data = form_data_with_customer.merge(customer_profile_id: nil)
      quote_submitter = Customers::Quotes::Submit.new(form_data: invalid_form_data).call
      expect(quote_submitter).to_not be_success

      expect(Rails.cache.exist?(cache_key)).to be_truthy
    end

    it 'does not invalidate cache if customer profile is blank' do
      quote_submitter = Customers::Quotes::Submit.new(form_data: form_data).call
      expect(quote_submitter).to be_success

      expect { quote_submitter }.to_not raise_error
    end
  end

  context 'errors' do
    it 'cannot create a quote  passed in profile is a supplier' do
      quote_profile = create(:supplier_profile, :random)

      quote_submitter = Customers::Quotes::Submit.new(form_data: form_data, profile: quote_profile).call

      expect(quote_submitter).to_not be_success
      expect(quote_submitter.errors).to include('Only customers can submit a quote')
    end

    it 'cannot create a quote without a type (kind)' do
      form_data_without_kind = form_data.except(:type)
      quote_submitter = Customers::Quotes::Submit.new(form_data: form_data_without_kind).call

      expect(quote_submitter).to_not be_success
      expect(quote_submitter.errors).to include('Need quote type')
    end

    it 'errors if the quote data isn\'t valid' do
      form_data_with_invalid_kind = form_data.merge({ type: 'invalid-kind' })
      quote_submitter = Customers::Quotes::Submit.new(form_data: form_data_with_invalid_kind).call

      expect(quote_submitter).to_not be_success
      expect(quote_submitter.errors).to include('Kind is not included in the list')
    end

    it 'does not send a notification do admin or customer if the quote failed', notifications: true do
      error_test_1 = [true, false].sample
      error_test_2 = [true, false].sample
      invalid_form_data = case
      when error_test_1
        form_data.except(:type)
      when error_test_2
        form_data.merge({ type: 'invalid-kind' })
      else
        {}
      end

      expect(Admin::Emails::SendCustomerQuoteEmail).to_not receive(:new)
      expect(Customers::Emails::SendCustomerQuoteEmail).to_not receive(:new)

      quote_submitter = Customers::Quotes::Submit.new(form_data: invalid_form_data).call
      expect(quote_submitter).to_not be_success
    end
  end

end