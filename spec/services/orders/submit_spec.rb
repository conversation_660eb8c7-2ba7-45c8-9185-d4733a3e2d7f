require 'rails_helper'

RSpec.describe Orders::Submit, type: :service, orders: true, suppliers: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:supplier) { create(:supplier_profile, :random) }
  let(:suburb) { create(:suburb, :random) }

  let!(:lead_time_fetcher) { double(Orders::GetMaximumLeadTime) }
  let!(:supplier_spends_fetcher) { double(Orders::GetSupplierSpends) }

  before do
    # mock that the customer email will be sent
    customer_email_sender = delayed_customer_email_sender = double(Customers::Emails::SendNewOrderEmail)
    allow(Customers::Emails::SendNewOrderEmail).to receive(:new).and_return(customer_email_sender)
    allow(customer_email_sender).to receive(:delay).and_return(delayed_customer_email_sender)
    allow(delayed_customer_email_sender).to receive(:call)

    # mock that the supplier emails will be sent
    supplier_email_sender = delayed_supplier_email_sender = double(Suppliers::Emails::SendNewOrderEmail)
    allow(Suppliers::Emails::SendNewOrderEmail).to receive(:new).and_return(supplier_email_sender)
    allow(supplier_email_sender).to receive(:delay).and_return(delayed_supplier_email_sender)
    allow(delayed_supplier_email_sender).to receive(:call)

    # mock that account manager notifications
    account_mananger_notifications_sender = delayed_account_mananger_notifications_sender = double(Orders::Notifications::NotifyAccountManagers)
    allow(Orders::Notifications::NotifyAccountManagers).to receive(:new).and_return(account_mananger_notifications_sender)
    allow(account_mananger_notifications_sender).to receive(:delay).and_return(delayed_account_mananger_notifications_sender)
    allow(delayed_account_mananger_notifications_sender).to receive(:call)

    # mock customer syncer
    contact_syncer = delayed_syncer = double(Hubspot::SyncContact)
    allow(Hubspot::SyncContact).to receive(:new).and_return(contact_syncer)
    allow(contact_syncer).to receive(:delay).and_return(delayed_syncer)
    allow(delayed_syncer).to receive(:call).and_return(true)

    # mock on-hold charge
    order_charger = delayed_order_charger = double(Orders::OnHoldCharges::Upsert)
    allow(Orders::OnHoldCharges::Upsert).to receive(:new).and_return(order_charger)
    allow(order_charger).to receive(:delay).and_return(delayed_order_charger)
    allow(delayed_order_charger).to receive(:call).and_return(true)

    # mock promotions syncer
    promotions_syncer = double(Promotions::SyncWithOrder)
    allow(Promotions::SyncWithOrder).to receive(:new).and_return(promotions_syncer)
    allow(promotions_syncer).to receive(:call).and_return(true)

    # mock lead time fetcher
    allow(Orders::GetMaximumLeadTime).to receive(:new).and_return(lead_time_fetcher)
    valid_lead_time_response = OpenStruct.new(can_process?: true)
    allow(lead_time_fetcher).to receive(:call).and_return(valid_lead_time_response)

    # mock supplier spends
    allow(Orders::GetSupplierSpends).to receive(:new).and_return(supplier_spends_fetcher)
    valid_supplier_spend_response = OpenStruct.new(is_under?: false)
    allow(supplier_spends_fetcher).to receive(:call).and_return(valid_supplier_spend_response)

    # mock event log creator
    event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:call).and_return(true)

    # mock saving major category
    major_category_saver = delayed_major_category_saver = double(Orders::RetrieveMajorOrderCategory)
    allow(Orders::RetrieveMajorOrderCategory).to receive(:new).and_return(major_category_saver)
    allow(major_category_saver).to receive(:delay).and_return(delayed_major_category_saver)
    allow(delayed_major_category_saver).to receive(:call).and_return(true)
  end

  context 'for a one-off order' do
    let!(:order) { create(:order, :draft, customer_profile: nil) }
    let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier) }

    it 'submits the order' do
      order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call

      expect(order_submitter).to be_success
      submitted_order = order_submitter.order
      expect(submitted_order.id).to eq(order.id)
      expect(submitted_order.status).to eq('new')
    end

    it 'sets the customer of the order the passed in customer' do
      order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call

      expect(order_submitter).to be_success
      submitted_order = order_submitter.order
      expect(submitted_order.id).to eq(order.id)
      expect(submitted_order.customer_profile).to eq(customer)
    end

    it 'submits order with the passed in order details (inc delivery_at)' do
      order_params = { name: Faker::Name.name, delivery_at: Time.zone.now, number_of_people: 10, department_identity: Faker::Name.name }
      order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

      expect(order_submitter).to be_success
      submitted_order = order_submitter.order
      expect(submitted_order.name).to eq(order_params[:name])
      expect(submitted_order.delivery_at).to eq(order_params[:delivery_at])
      expect(submitted_order.number_of_people).to eq(order_params[:number_of_people])
      expect(submitted_order.department_identity).to eq(order_params[:department_identity])
    end

    it 'submits order with the passed in delivery details' do
      order_params = { delivery_address_level: Faker::Address.secondary_address, delivery_address: Faker::Address.street_address, delivery_suburb_id: suburb.id, delivery_instruction: Faker::Lorem.sentence }
      order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

      expect(order_submitter).to be_success
      submitted_order = order_submitter.order
      expect(submitted_order.delivery_address_level).to eq(order_params[:delivery_address_level])
      expect(submitted_order.delivery_address).to eq(order_params[:delivery_address])
      expect(submitted_order.delivery_suburb).to eq(suburb)
      expect(submitted_order.delivery_instruction).to eq(order_params[:delivery_instruction])
    end

    it 'submits order with the passed in contact details' do
      order_params = { contact_name: Faker::Name.name, company_name: Faker::Company.name, contact_email: Faker::Internet.email, phone: Faker::PhoneNumber.phone_number }
      order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

      expect(order_submitter).to be_success
      submitted_order = order_submitter.order
      expect(submitted_order.contact_name).to eq(order_params[:contact_name])
      expect(submitted_order.company_name).to eq(order_params[:company_name])
      expect(submitted_order.contact_email).to eq(order_params[:contact_email])
      expect(submitted_order.phone).to eq(order_params[:phone])
    end

    context 'with a credit card' do
      let(:credit_card) { create(:credit_card, :random) }
      let(:totals) { double(Orders::CalculateCustomerTotals) }
      before do
        customer.credit_cards << credit_card
        allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(totals)
        allow(totals).to receive(:call)
      end

      it 'submits order with the passed in Credit card details and recalculates totals based on credit card surcharge' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)

        order_params = { credit_card_id: credit_card.id }
        order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_order = order_submitter.order
        expect(submitted_order.credit_card).to eq(credit_card)
      end

      context 'on-hold charge' do
        it 'creates a on-hold charge for an order with an attached stripe credit card' do
          credit_card.update_column(:stripe_token, SecureRandom.hex(7))
          expect(Orders::OnHoldCharges::Upsert).to receive(:new).with(order: anything)

          order_params = { credit_card_id: credit_card.id }
          order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call
          expect(order_submitter).to be_success
        end

        it 'does not create a on-hold charge if attached credit card is pay on account' do
          credit_card.update_column(:pay_on_account, true)
          expect(Orders::OnHoldCharges::Upsert).to_not receive(:new)

          order_params = { credit_card_id: credit_card.id }
          order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call
          expect(order_submitter).to be_success
        end

        it 'does not create a on-hold charge if attached credit card is not a stripe card' do
          credit_card.update_column(:stripe_token, nil)
          expect(Orders::OnHoldCharges::Upsert).to_not receive(:new)

          order_params = { credit_card_id: credit_card.id }
          order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call
          expect(order_submitter).to be_success
        end
      end
    end

    context 'with purchase order detials', purchase_orders: true do
      let!(:customer_purchase_order) { create(:customer_purchase_order, customer_profile: customer, po_number: SecureRandom.hex(7)) }

      it 'saves the purchase order based on passed in ID' do
        order_params = { cpo_id: customer_purchase_order.id }
        order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_order = order_submitter.order
        expect(submitted_order.cpo_id).to eq(customer_purchase_order.id)
        expect(submitted_order.po_number).to eq(customer_purchase_order.po_number)
      end

      it 'saves the purchase order based on passed in PO number' do
        order_params = { cpo_id: customer_purchase_order.po_number }
        order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_order = order_submitter.order
        expect(submitted_order.cpo_id).to eq(customer_purchase_order.id)
        expect(submitted_order.po_number).to eq(customer_purchase_order.po_number)
      end

      it 'creates and saves a new customer purchase order based on passed in PO number' do
        order_params = { cpo_id: customer_purchase_order.po_number + SecureRandom.hex(7) }
        order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_order = order_submitter.order
        expect(submitted_order.customer_purchase_order).to be_present
        expect(submitted_order.customer_purchase_order.id).to_not eq(customer_purchase_order.id)
        expect(submitted_order.customer_purchase_order.customer_profile).to eq(order.customer_profile)
        expect(submitted_order.po_number).to eq(order_params[:cpo_id])
      end
    end

    context 'with GST-free purchase order detials', purchase_orders: true do
      let!(:gst_free_customer_purchase_order) { create(:customer_purchase_order, customer_profile: customer, po_number: SecureRandom.hex(7)) }

      it 'saves the GST-free purchase order based on passed in ID' do
        order_params = { gst_free_cpo_id: gst_free_customer_purchase_order.id }
        order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_order = order_submitter.order
        expect(submitted_order.gst_free_cpo_id).to eq(gst_free_customer_purchase_order.id)
        expect(submitted_order.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
      end

      it 'saves the GST-free purchase order based on passed in PO number' do
        order_params = { gst_free_cpo_id: gst_free_customer_purchase_order.po_number }
        order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_order = order_submitter.order
        expect(submitted_order.gst_free_cpo_id).to eq(gst_free_customer_purchase_order.id)
        expect(submitted_order.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
      end

      it 'creates and saves a new customer purchase order based on passed in PO number' do
        order_params = { gst_free_cpo_id: gst_free_customer_purchase_order.po_number + SecureRandom.hex(7) }
        order_submitter = Orders::Submit.new(order: order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_order = order_submitter.order
        expect(submitted_order.gst_free_customer_purchase_order).to be_present
        expect(submitted_order.gst_free_customer_purchase_order.id).to_not eq(gst_free_customer_purchase_order.id)
        expect(submitted_order.gst_free_customer_purchase_order.customer_profile).to eq(order.customer_profile)
        expect(submitted_order.gst_free_po_number).to eq(order_params[:gst_free_cpo_id])
      end
    end

    it 'does not checkout to woolworths on a normal order', woolworths: true do
      expect(Woolworths::API::CheckoutOrder).to_not receive(:new)
      order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
      expect(order_submitter).to be_success
    end

    context 'for a Woolworths order', woolworths: true do
      before do
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(anything).and_return('')
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :host).and_return('')
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:amazon, :supplier_profile_id).and_return(nil)
        order_checkout = delayed_checkout = double(Woolworths::API::CheckoutOrder)
        allow(Woolworths::API::CheckoutOrder).to receive(:new).and_return(order_checkout)
        allow(order_checkout).to receive(:new).and_return(delayed_checkout)
        allow(delayed_checkout).to receive(:call).and_return(true)
      end

      it 'checks out the order on Woolworths' do
        expect(Woolworths::API::CheckoutOrder).to receive(:new).with(order: order)

        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call

        expect(order_submitter).to be_success
      end
    end

    context 'for an Amazon Order', amazon: true do
      before do
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(anything).and_return('')
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, anything).and_return('')
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:amazon, :supplier_profile_id).and_return(supplier.id)
      end

      it 'request an Event Log for new amazon order to be created', event_logs: true do
        expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'new-amazon-order', severity: 'warning')

        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
        expect(order_submitter).to be_success
      end
    end

    context 'with an attached customer quote', quotes: true do
      let!(:customer_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'submitted') }

      before do
        order.update_column(:customer_quote_id, customer_quote.id)
      end

      it 'marks the customer quote as `accepted`' do
        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
        expect(order_submitter).to be_success

        expect(customer_quote.reload.status).to eq('accepted')
      end
    end

    context 'customer email' do
      it 'sends an email to the order\'s customer' do
        expect(Customers::Emails::SendNewOrderEmail).to receive(:new).with(order: order, customer: customer)

        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
        expect(order_submitter).to be_success
      end
    end

    context 'supplier email(s)' do
      let(:supplier2) { create(:supplier_profile, :random) }
      let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

      it 'creates order_supplier records for the order\'s suppliers' do
        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call

        expect(order_submitter).to be_success

        order_suppliers = order.order_suppliers
        expect(order_suppliers.size).to eq(2)
        expect(order_suppliers.map(&:supplier_profile)).to include(supplier, supplier2)
      end

      it 'sends an email to each supplier' do
        expect(Suppliers::Emails::SendNewOrderEmail).to receive(:new).with(order: order, supplier: supplier)
        expect(Suppliers::Emails::SendNewOrderEmail).to receive(:new).with(order: order, supplier: supplier2)

        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call

        expect(order_submitter).to be_success
      end
    end

    context 'admin emails' do
      it 'requests sending of account manager notifications' do
        expect(Orders::Notifications::NotifyAccountManagers).to receive(:new).with(order: order)

        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
        expect(order_submitter).to be_success
      end
    end

    context 'with a coupon', coupons: true do
      let!(:coupon) { create(:coupon, :random) }
      let!(:coupon_redeemder) { double(Coupons::Redeem) }
      before do
        # attach coupon to order
        order.update_column(:coupon_id, coupon.id)
      end

      it 'redeems the coupon for the order' do
        expect(Coupons::Redeem).to receive(:new).with(coupon: coupon, order: order).and_return(coupon_redeemder)
        expect(coupon_redeemder).to receive(:call)

        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
        expect(order_submitter).to be_success
      end
    end

    it 'makes a request to sync with promotions', promotions: true do
      expect(Promotions::SyncWithOrder).to receive(:new).with(order: order, customer: customer)

      order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
      expect(order_submitter).to be_success
    end

    it 'syncs with the Hubspot customer account', hubspot: true do
      expect(Hubspot::SyncContact).to receive(:new).with(contact: customer.user)

      order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
      expect(order_submitter).to be_success
    end

    context 'Event Logs', event_logs: true do
      it 'request an Event Log for order submission be created' do
        expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'new-order-submitted', severity: 'info')

        order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
        expect(order_submitter).to be_success
      end

      context 'when order is submitted after supplier cutoff' do
        before do
          invalid_lead_time_response = OpenStruct.new(can_process?: false)
          allow(lead_time_fetcher).to receive(:call).and_return(invalid_lead_time_response)
        end

        it 'request an Event Log for order submission be created with additional lead time info' do
          expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'new-order-submitted', severity: 'warning', after_cutoff: true)

          order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
          expect(order_submitter).to be_success
        end
      end # Order within supplier lead time

      context 'when order is submitted below supplier minimum spends' do
        let!(:supplier) { create(:supplier_profile, :random) }
        let!(:supplier_spend) { SupplierSpend.new(supplier: supplier, total_spend: 100, minimum_spend: 200) }

        before do
          invalid_spend_response = OpenStruct.new(is_under?: true, supplier_spends: [supplier_spend])
          allow(supplier_spends_fetcher).to receive(:call).and_return(invalid_spend_response)
        end

        it 'request an Event Log for order submission be created with additional spends info' do
          expected_supplier_spend = {
            name: supplier.name,
            minimum: supplier_spend.minimum_spend,
            remaining: (supplier_spend.minimum_spend - supplier_spend.total_spend),
          }
          expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'new-order-submitted', severity: 'warning', under_supplier_minimum: true, supplier_spends: [expected_supplier_spend])

          order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
          expect(order_submitter).to be_success
        end
      end # Order below supplier minimums

      context 'for a Custom Order', custom_orders: true do
        before do
          order.update_column(:order_variant, 'event_order')
        end

        it 'request an Event Log for `New Custom Order Submitted` to be created' do
          expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'new-custom-order-submitted', severity: 'info')

          order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
          expect(order_submitter).to be_success
        end

        it 'request an Event Log for `Margin Below Threshold` to be created if order commission is below threshold ' do
          commission = rand(0.0..24.9)
          order.update_column(:commission, commission)
          yordar_commission = ((1 - (1 - (0.0 / 100)) / (1 + (commission.to_f / 100))) * 100).round(2)
          expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'order-below-margin-threshold', severity: 'warning', commission: yordar_commission)

          order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
          expect(order_submitter).to be_success
        end

        it 'does not request an Event Log for `Margin Below Threshold` to be created if order commission above threshold ' do
          order.update_column(:commission, rand(25.0..50.9))
          expect(EventLogs::Create).to_not receive(:new).with(event_object: order, event: 'order-below-margin-threshold', severity: anything, commission: anything)

          order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
          expect(order_submitter).to be_success
        end
      end
    end # Event Logs

    it 'makes a request to save the major category of an order' do
      expect(Orders::RetrieveMajorOrderCategory).to receive(:new).with(order: order, save_category: true)

      order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
      expect(order_submitter).to be_success
    end
  end # One-off Order

  context 'for recurring orders' do
    let!(:mon_order) { create(:order, :draft, customer_profile: nil, name: 'Mon') }
    let!(:wed_order) { create(:order, :draft, customer_profile: nil, name: 'Wed') }
    let!(:fri_order) { create(:order, :draft, customer_profile: nil, name: 'Fri') }

    let!(:order_line1) { create(:order_line, :random, order: mon_order, supplier_profile: supplier) }
    let!(:order_line2) { create(:order_line, :random, order: wed_order, supplier_profile: supplier) }

    before do
      [mon_order, wed_order, fri_order].each do |order|
        order.update_columns(order_type: 'recurrent', recurrent_id: mon_order.id, template_id: order.id, pattern: '1.week')
      end
    end

    it 'only submit the orders with any order lines' do
      order_submitter = Orders::Submit.new(order: mon_order, order_params: {}, customer: customer).call

      expect(order_submitter).to be_success
      submitted_order = order_submitter.order
      expect(submitted_order.id).to eq(mon_order.id)

      submitted_recurrent_orders = order_submitter.recurrent_orders
      expect(submitted_recurrent_orders.map(&:id)).to include(mon_order.id, wed_order.id)
      expect(submitted_recurrent_orders.map(&:id)).to_not include(fri_order.id)
      expect(submitted_recurrent_orders.map(&:status).uniq).to eq(['new'])

      expect(mon_order.reload.status).to eq('new')
      expect(wed_order.reload.status).to eq('new')
      expect(fri_order.reload.status).to_not eq('new')
    end

    it 'sets the correct delivery date based on passed in date and the day of the recurrent order' do
      base_delivery = Time.zone.now.beginning_of_week + 10.hours + 20.minutes
      order_params = { delivery_at: base_delivery }
      order_submitter = Orders::Submit.new(order: mon_order, order_params: order_params, customer: customer).call

      expect(order_submitter).to be_success

      expect(mon_order.reload.delivery_at).to eq(base_delivery)
      expect(wed_order.reload.delivery_at).to eq(base_delivery + 2.days)
    end

    it 'copies all information to individual orders' do
      order_params = { name: Faker::Name.name, number_of_people: 10, department_identity: Faker::Name.name }
      order_params = order_params.merge({ delivery_address_level: Faker::Address.secondary_address, delivery_address: Faker::Address.street_address, delivery_suburb_id: suburb.id, delivery_instruction: Faker::Lorem.sentence })
      order_params = order_params.merge({ contact_name: Faker::Name.name, company_name: Faker::Company.name, contact_email: Faker::Internet.email, phone: Faker::PhoneNumber.phone_number })

      order_submitter = Orders::Submit.new(order: mon_order, order_params: order_params, customer: customer).call

      expect(order_submitter).to be_success
      submitted_recurrent_orders = order_submitter.recurrent_orders
      expect(submitted_recurrent_orders.map(&:name).uniq).to eq([order_params[:name]])
      expect(submitted_recurrent_orders.map(&:number_of_people).uniq).to eq([order_params[:number_of_people]])
      expect(submitted_recurrent_orders.map(&:department_identity).uniq).to eq([order_params[:department_identity]])
      expect(submitted_recurrent_orders.map(&:delivery_address_level).uniq).to eq([order_params[:delivery_address_level]])
      expect(submitted_recurrent_orders.map(&:delivery_address).uniq).to eq([order_params[:delivery_address]])
      expect(submitted_recurrent_orders.map(&:delivery_suburb).uniq).to eq([suburb])
      expect(submitted_recurrent_orders.map(&:delivery_instruction).uniq).to eq([order_params[:delivery_instruction]])
      expect(submitted_recurrent_orders.map(&:contact_name).uniq).to eq([order_params[:contact_name]])
      expect(submitted_recurrent_orders.map(&:company_name).uniq).to eq([order_params[:company_name]])
      expect(submitted_recurrent_orders.map(&:contact_email).uniq).to eq([order_params[:contact_email]])
      expect(submitted_recurrent_orders.map(&:phone).uniq).to eq([order_params[:phone]])
    end

    context 'with a credit card' do
      let(:credit_card) { create(:credit_card, :random) }
      let(:totals) { double(Orders::CalculateCustomerTotals) }
      before do
        customer.credit_cards << credit_card
        allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(totals)
        allow(totals).to receive(:call)
      end

      it 'submits all order with the passed in Credit card details and recalculates totals based on credit card surcharge' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: mon_order, save_totals: true)
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: wed_order, save_totals: true)
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: fri_order, save_totals: true)

        order_params = { credit_card_id: credit_card.id }
        order_submitter = Orders::Submit.new(order: mon_order, order_params: order_params, customer: customer).call

        expect(order_submitter).to be_success
        submitted_recurrent_orders = order_submitter.recurrent_orders
        expect(submitted_recurrent_orders.map(&:credit_card).uniq).to eq([credit_card])
      end

      it 'does not create a on-hold charge for a recurring order' do
        credit_card.update_column(:stripe_token, SecureRandom.hex(7)) # even if its a stripe card
        expect(Orders::OnHoldCharges::Upsert).to_not receive(:new)

        order_params = { credit_card_id: credit_card.id }
        order_submitter = Orders::Submit.new(order: mon_order, order_params: order_params, customer: customer).call
        expect(order_submitter).to be_success
      end
    end

    context 'customer email' do
      it 'sends an email to customer for each order' do
        expect(Customers::Emails::SendNewOrderEmail).to receive(:new).with(order: mon_order, customer: customer)
        expect(Customers::Emails::SendNewOrderEmail).to receive(:new).with(order: wed_order, customer: customer)

        order_submitter = Orders::Submit.new(order: mon_order, order_params: {}, customer: customer).call
        expect(order_submitter).to be_success
      end
    end

    context 'supplier email(s)' do
      it 'creates order suppliers for each notifiable order' do
        order_submitter = Orders::Submit.new(order: mon_order, order_params: {}, customer: customer).call

        expect(order_submitter).to be_success

        mon_order_suppliers = mon_order.reload.order_suppliers
        expect(mon_order_suppliers.size).to eq(1)
        expect(mon_order_suppliers.last.supplier_profile).to eq(supplier)

        wed_order_suppliers = wed_order.reload.order_suppliers
        expect(wed_order_suppliers.size).to eq(1)
        expect(wed_order_suppliers.last.supplier_profile).to eq(supplier)
      end

      it 'sends and email to each supplier for each order' do
        expect(Suppliers::Emails::SendNewOrderEmail).to receive(:new).with(order: mon_order, supplier: supplier)
        expect(Suppliers::Emails::SendNewOrderEmail).to receive(:new).with(order: wed_order, supplier: supplier)

        order_submitter = Orders::Submit.new(order: mon_order, order_params: {}, customer: customer).call

        expect(order_submitter).to be_success
      end
    end

    it 'does not notify the account manager' do
      expect(Orders::Notifications::NotifyAccountManagers).to_not receive(:new)

      order_submitter = Orders::Submit.new(order: mon_order, order_params: {}, customer: customer).call
      expect(order_submitter).to be_success
    end
  end

  context 'cache invalidation for quoted orders' do
    let!(:quoted_order) { create(:order, :draft, customer_profile: customer, status: 'quoted') }
    let!(:quoted_order_line) { create(:order_line, :random, order: quoted_order) }

    before do
      quoted_order.reload # Ensure order_lines are loaded
    end

    it 'invalidates the quote count cache when a quoted order is submitted' do
      cache_key = "customer_profile_#{customer.id}_quote_count"

      Rails.cache.write(cache_key, 5)
      expect(Rails.cache.exist?(cache_key)).to be_truthy

      order_submitter = Orders::Submit.new(order: quoted_order, order_params: {}, customer: customer).call
      expect(order_submitter).to be_success
      expect(order_submitter.order.status).to eq('new')

      expect(Rails.cache.exist?(cache_key)).to be_falsey
    end

    it 'does not invalidate cache when a non-quoted order is submitted' do
      cache_key = "customer_profile_#{customer.id}_quote_count"

      Rails.cache.write(cache_key, 5)
      expect(Rails.cache.exist?(cache_key)).to be_truthy

      # Ensure the draft order has order lines
      create(:order_line, :random, order: order)
      order.reload

      order_submitter = Orders::Submit.new(order: order, order_params: {}, customer: customer).call
      expect(order_submitter).to be_success
      expect(order_submitter.order.status).to eq('new')

      expect(Rails.cache.exist?(cache_key)).to be_truthy
    end

    it 'does not invalidate cache if order submission fails due to no order lines' do
      cache_key = "customer_profile_#{customer.id}_quote_count"

      Rails.cache.write(cache_key, 5)
      expect(Rails.cache.exist?(cache_key)).to be_truthy

      # Remove order lines to make submission skip this order
      quoted_order.order_lines.destroy_all
      quoted_order.reload

      order_submitter = Orders::Submit.new(order: quoted_order, order_params: {}, customer: customer).call
      # Service succeeds but doesn't process orders without order lines
      expect(order_submitter).to be_success
      expect(order_submitter.order).to be_nil # No order processed

      expect(Rails.cache.exist?(cache_key)).to be_truthy
    end

    it 'does not invalidate cache if order update fails' do
      cache_key = "customer_profile_#{customer.id}_quote_count"

      Rails.cache.write(cache_key, 5)
      expect(Rails.cache.exist?(cache_key)).to be_truthy

      # Force Orders::Update to fail by providing invalid delivery_at
      invalid_params = { delivery_at: 'invalid-date' }

      order_submitter = Orders::Submit.new(order: quoted_order, order_params: invalid_params, customer: customer).call
      expect(order_submitter).to_not be_success

      expect(Rails.cache.exist?(cache_key)).to be_truthy
    end

    it 'handles missing customer profile gracefully' do
      # Test the service method directly since we can't create orders without customer_profile
      service = Orders::Submit.new(order: quoted_order, order_params: {}, customer: customer)

      # Mock the order to return nil customer_profile
      allow(quoted_order).to receive(:customer_profile).and_return(nil)

      # Should not raise any errors when calling invalidate_quote_count_cache_for
      expect { service.send(:invalidate_quote_count_cache_for, quoted_order) }.to_not raise_error
    end
  end

end
