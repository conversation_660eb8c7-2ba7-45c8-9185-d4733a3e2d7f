require 'rails_helper'

RSpec.describe CustomerProfileHelper, type: :helper do

  describe '#quote_count_for' do
    let!(:customer) { create(:customer_profile, :random) }

    context 'with no quotes or orders' do
      it 'returns 0 when customer has no quotes or quoted orders' do
        expect(helper.quote_count_for(customer)).to eq(0)
      end

      it 'returns 0 when customer is nil' do
        expect(helper.quote_count_for(nil)).to eq(0)
      end
    end

    context 'with customer quotes' do
      let!(:submitted_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'submitted') }
      let!(:quoted_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'quoted') }
      let!(:accepted_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'accepted') }
      let!(:archived_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'archived') }

      it 'counts submitted and quoted customer quotes' do
        expect(helper.quote_count_for(customer)).to eq(2) # submitted + quoted
      end

      it 'does not count accepted or archived quotes' do
        # Only submitted and quoted should be counted
        expect(helper.quote_count_for(customer)).to eq(2)
      end
    end

    context 'with quoted orders' do
      let!(:quoted_order1) { create(:order, :draft, customer_profile: customer, status: 'quoted') }
      let!(:quoted_order2) { create(:order, :draft, customer_profile: customer, status: 'quoted') }
      let!(:new_order) { create(:order, :draft, customer_profile: customer, status: 'new') }
      let!(:confirmed_order) { create(:order, :draft, customer_profile: customer, status: 'confirmed') }

      it 'counts only orders with quoted status' do
        expect(helper.quote_count_for(customer)).to eq(2) # 2 quoted orders
      end

      it 'does not count orders with other statuses' do
        # Only quoted orders should be counted
        expect(helper.quote_count_for(customer)).to eq(2)
      end
    end

    context 'with both customer quotes and quoted orders' do
      let!(:submitted_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'submitted') }
      let!(:quoted_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'quoted') }
      let!(:quoted_order1) { create(:order, :draft, customer_profile: customer, status: 'quoted') }
      let!(:quoted_order2) { create(:order, :draft, customer_profile: customer, status: 'quoted') }

      it 'returns the sum of both quote requests and quoted orders' do
        expect(helper.quote_count_for(customer)).to eq(4) # 2 quotes + 2 quoted orders
      end
    end

    context 'caching behavior' do
      let!(:submitted_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'submitted') }
      let!(:quoted_order) { create(:order, :draft, customer_profile: customer, status: 'quoted') }

      it 'caches the result for 12 hours' do
        # Clear any existing cache
        Rails.cache.clear

        # First call should hit the database and cache the result
        expect(customer.quotes).to receive(:where).with(status: %w[submitted quoted]).and_call_original
        expect(customer.orders).to receive(:where).with(status: 'quoted').and_call_original
        
        result1 = helper.quote_count_for(customer)
        expect(result1).to eq(2)

        # Second call should use cached result (no database queries)
        expect(customer.quotes).to_not receive(:where)
        expect(customer.orders).to_not receive(:where)
        
        result2 = helper.quote_count_for(customer)
        expect(result2).to eq(2)
      end

      it 'uses the correct cache key format' do
        cache_key = "customer_profile_#{customer.id}_quote_count"
        
        # Clear cache
        Rails.cache.delete(cache_key)
        
        # Call method to populate cache
        helper.quote_count_for(customer)
        
        # Verify cache exists with correct key
        expect(Rails.cache.exist?(cache_key)).to be_truthy
        expect(Rails.cache.read(cache_key)).to eq(2)
      end

      it 'returns cached value when available' do
        cache_key = "customer_profile_#{customer.id}_quote_count"
        
        # Pre-populate cache with a different value
        Rails.cache.write(cache_key, 99)
        
        # Should return cached value, not calculated value
        result = helper.quote_count_for(customer)
        expect(result).to eq(99)
      end

      it 'recalculates when cache expires' do
        cache_key = "customer_profile_#{customer.id}_quote_count"
        
        # Set cache with short expiry
        Rails.cache.write(cache_key, 99, expires_in: 1.second)
        
        # Should return cached value initially
        expect(helper.quote_count_for(customer)).to eq(99)
        
        # Wait for cache to expire
        sleep(2)
        
        # Should recalculate and return actual value
        expect(helper.quote_count_for(customer)).to eq(2)
      end
    end

    context 'edge cases' do
      it 'handles customer with no quotes association gracefully' do
        # Mock customer without quotes
        customer_without_quotes = double('CustomerProfile', id: 123, quotes: double(where: double(count: 0)), orders: double(where: double(count: 0)))
        
        expect(helper.quote_count_for(customer_without_quotes)).to eq(0)
      end

      it 'handles database errors gracefully' do
        # Mock database error
        allow(customer.quotes).to receive(:where).and_raise(ActiveRecord::StatementInvalid.new('Database error'))
        
        expect { helper.quote_count_for(customer) }.to raise_error(ActiveRecord::StatementInvalid)
      end
    end

    context 'integration with actual quote count logic' do
      let!(:quote_request) { create(:customer_quote, :random, customer_profile: customer, status: 'submitted') }
      let!(:quoted_order) { create(:order, :draft, customer_profile: customer, status: 'quoted') }

      it 'matches the logic used in the quotes API and frontend' do
        # This should match the same logic as:
        # - API::QuotesController#index
        # - QuotesList component filtering
        # - Sidebar quote count display
        
        api_quote_count = customer.quotes.where(status: %w[submitted quoted]).count + 
                         customer.orders.where(status: 'quoted').count
        
        helper_quote_count = helper.quote_count_for(customer)
        
        expect(helper_quote_count).to eq(api_quote_count)
        expect(helper_quote_count).to eq(2) # 1 quote request + 1 quoted order
      end
    end
  end

end
